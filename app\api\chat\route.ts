import { Client, DeepInfra, Together } from "@/lib/ai-client"
import type { NextRequest } from "next/server"

export const runtime = "edge"

export async function POST(req: NextRequest) {
  try {
    const { messages, model, provider, apiKey } = await req.json()

    let client
    const options = apiKey ? { apiKey } : {}

    switch (provider) {
      case "deepinfra":
        client = new DeepInfra(options)
        break
      case "together":
        client = new Together(options)
        break
      case "pollinations":
      default:
        client = new Client({ defaultModel: "deepseek-reasoning", ...options })
        break
    }

    const stream = await client.chat.completions.create({
      model: model || client.defaultModel,
      messages,
      stream: true,
    })

    const encoder = new TextEncoder()
    const readable = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of stream) {
            const content = chunk.choices?.[0]?.delta?.content || ""
            if (content) {
              controller.enqueue(encoder.encode(`data: ${JSON.stringify({ content })}\n\n`))
            }
          }
          controller.enqueue(encoder.encode("data: [DONE]\n\n"))
          controller.close()
        } catch (error) {
          controller.error(error)
        }
      },
    })

    return new Response(readable, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    })
  } catch (error) {
    console.error("Chat API error:", error)
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}

"use client"

import { useState, useR<PERSON>, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, Settings, Sparkles, Bot, User, Plus, MessageSquare, Trash2, Copy, Check, Globe } from "lucide-react"
import { ThemeToggle } from "@/components/theme-toggle"
import { marked } from "marked"
import { markedHighlight } from "marked-highlight"
import katex from "katex"

import hljs from "highlight.js/lib/core"
import javascript from "highlight.js/lib/languages/javascript"
import typescript from "highlight.js/lib/languages/typescript"
import python from "highlight.js/lib/languages/python"
import bash from "highlight.js/lib/languages/bash"
import json from "highlight.js/lib/languages/json"
import xml from "highlight.js/lib/languages/xml" // html / xml
import css from "highlight.js/lib/languages/css"
import sql from "highlight.js/lib/languages/sql"
import java from "highlight.js/lib/languages/java"
import csharp from "highlight.js/lib/languages/csharp"
import php from "highlight.js/lib/languages/php"
import go from "highlight.js/lib/languages/go"
import rust from "highlight.js/lib/languages/rust"
import swift from "highlight.js/lib/languages/swift"
import kotlin from "highlight.js/lib/languages/kotlin"
import yaml from "highlight.js/lib/languages/yaml"
import dockerfile from "highlight.js/lib/languages/dockerfile"
import markdown from "highlight.js/lib/languages/markdown"
import ruby from "highlight.js/lib/languages/ruby"
import perl from "highlight.js/lib/languages/perl"
import lua from "highlight.js/lib/languages/lua"
import r from "highlight.js/lib/languages/r"
import scala from "highlight.js/lib/languages/scala"
import clojure from "highlight.js/lib/languages/clojure"
import haskell from "highlight.js/lib/languages/haskell"
import erlang from "highlight.js/lib/languages/erlang"
import elixir from "highlight.js/lib/languages/elixir"
import dart from "highlight.js/lib/languages/dart"
import groovy from "highlight.js/lib/languages/groovy"
import matlab from "highlight.js/lib/languages/matlab"
import latex from "highlight.js/lib/languages/latex"
import vim from "highlight.js/lib/languages/vim"
import ini from "highlight.js/lib/languages/ini"
import toml from "highlight.js/lib/languages/ini" // toml uses ini syntax
import nginx from "highlight.js/lib/languages/nginx"
import apache from "highlight.js/lib/languages/apache"
import makefile from "highlight.js/lib/languages/makefile"
import cmake from "highlight.js/lib/languages/cmake"
import diff from "highlight.js/lib/languages/diff"

// register comprehensive language support
hljs.registerLanguage("javascript", javascript)
hljs.registerLanguage("typescript", typescript)
hljs.registerLanguage("python", python)
hljs.registerLanguage("bash", bash)
hljs.registerLanguage("json", json)
hljs.registerLanguage("html", xml)
hljs.registerLanguage("xml", xml)
hljs.registerLanguage("css", css)
hljs.registerLanguage("sql", sql)
hljs.registerLanguage("java", java)
hljs.registerLanguage("csharp", csharp)
hljs.registerLanguage("php", php)
hljs.registerLanguage("go", go)
hljs.registerLanguage("rust", rust)
hljs.registerLanguage("swift", swift)
hljs.registerLanguage("kotlin", kotlin)
hljs.registerLanguage("yaml", yaml)
hljs.registerLanguage("yml", yaml)
hljs.registerLanguage("dockerfile", dockerfile)
hljs.registerLanguage("markdown", markdown)
hljs.registerLanguage("md", markdown)
hljs.registerLanguage("ruby", ruby)
hljs.registerLanguage("perl", perl)
hljs.registerLanguage("lua", lua)
hljs.registerLanguage("r", r)
hljs.registerLanguage("scala", scala)
hljs.registerLanguage("clojure", clojure)
hljs.registerLanguage("haskell", haskell)
hljs.registerLanguage("erlang", erlang)
hljs.registerLanguage("elixir", elixir)
hljs.registerLanguage("dart", dart)
hljs.registerLanguage("groovy", groovy)
hljs.registerLanguage("matlab", matlab)
hljs.registerLanguage("latex", latex)
hljs.registerLanguage("vim", vim)
hljs.registerLanguage("ini", ini)
hljs.registerLanguage("toml", toml)
hljs.registerLanguage("nginx", nginx)
hljs.registerLanguage("apache", apache)
hljs.registerLanguage("makefile", makefile)
hljs.registerLanguage("cmake", cmake)
hljs.registerLanguage("diff", diff)

interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date
}

interface Chat {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
}

interface Model {
  id: string
  name?: string
}



const providers = [
  { id: "pollinations", name: "Pollinations.AI", requiresKey: false },
  { id: "deepinfra", name: "DeepInfra", requiresKey: true },
  { id: "together", name: "Together", requiresKey: true },
]

// Enhanced language aliases for better code detection
const languageAliases: Record<string, string> = {
  js: "javascript",
  jsx: "javascript",
  ts: "typescript",
  tsx: "typescript",
  py: "python",
  sh: "bash",
  shell: "bash",
  zsh: "bash",
  fish: "bash",
  powershell: "bash",
  ps1: "bash",
  html: "html",
  htm: "html",
  vue: "html",
  svelte: "html",
  xml: "xml",
  yml: "yaml",
  dockerfile: "dockerfile",
  docker: "dockerfile",
  md: "markdown",
  cs: "csharp",
  "c#": "csharp",
  kt: "kotlin",
  rs: "rust",
  rb: "ruby",
  pl: "perl",
  lua: "lua",
  r: "r",
  scala: "scala",
  clj: "clojure",
  cljs: "clojure",
  elm: "elm",
  haskell: "haskell",
  hs: "haskell",
  erlang: "erlang",
  elixir: "elixir",
  dart: "dart",
  groovy: "groovy",
  matlab: "matlab",
  octave: "matlab",
  tex: "latex",
  latex: "latex",
  vim: "vim",
  ini: "ini",
  toml: "toml",
  cfg: "ini",
  conf: "ini",
  nginx: "nginx",
  apache: "apache",
  makefile: "makefile",
  cmake: "cmake",
  diff: "diff",
  patch: "diff",
}

// Configure marked with enhanced syntax highlighting
marked.use(
  markedHighlight({
    langPrefix: "hljs language-",
    highlight(code, lang) {
      if (!lang) {
        const result = hljs.highlightAuto(code)
        return result.value
      }

      const language = languageAliases[lang.toLowerCase()] || lang.toLowerCase()

      if (hljs.getLanguage(language)) {
        try {
          return hljs.highlight(code, { language }).value
        } catch (error) {
          console.warn(`Failed to highlight code with language "${language}":`, error)
          return hljs.highlightAuto(code).value
        }
      }

      // fallback to auto-detection
      return hljs.highlightAuto(code).value
    },
  }),
)

// Enhanced marked options with better formatting
marked.setOptions({
  breaks: true,
  gfm: true,
  pedantic: false,
})

// Function to enhance code blocks with copy functionality
const enhanceCodeBlocks = (html: string): string => {
  return html.replace(
    /<pre class="hljs language-([^"]*)"[^>]*><code[^>]*>([\s\S]*?)<\/code><\/pre>/g,
    (_match, language, code) => {
      const cleanCode = code
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")

      const encodedCode = encodeURIComponent(cleanCode)

      return `<div class="code-block-wrapper">
        <div class="code-block-header">
          <span class="language-label">${language || 'text'}</span>
          <button class="copy-button" onclick="copyToClipboard(this)" data-code="${encodedCode}" title="Copy code">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            Copy
          </button>
        </div>
        <pre class="hljs language-${language || 'text'}"><code>${code}</code></pre>
      </div>`
    }
  ).replace(
    /<pre class="hljs"[^>]*><code[^>]*>([\s\S]*?)<\/code><\/pre>/g,
    (match, code) => {
      const cleanCode = code
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")

      const encodedCode = encodeURIComponent(cleanCode)

      return `<div class="code-block-wrapper">
        <div class="code-block-header">
          <span class="language-label">auto-detected</span>
          <button class="copy-button" onclick="copyToClipboard(this)" data-code="${encodedCode}" title="Copy code">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            Copy
          </button>
        </div>
        <pre class="hljs"><code>${code}</code></pre>
      </div>`
    }
  )
}

// Add custom extensions for enhanced formatting
marked.use({
  extensions: [
    {
      name: 'callout',
      level: 'block',
      start(src: string) {
        return src.match(/^\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)\]/)?.index
      },
      tokenizer(src: string) {
        const rule = /^\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)\]\s*([\s\S]*?)(?=\n\n|\n$|$)/
        const match = rule.exec(src)
        if (match) {
          return {
            type: 'callout',
            raw: match[0],
            calloutType: match[1],
            text: match[2].trim()
          }
        }
      },
      renderer(token: any) {
        const typeClass = token.calloutType.toLowerCase()
        const icons: Record<string, string> = {
          note: "ℹ️",
          tip: "💡",
          important: "❗",
          warning: "⚠️",
          caution: "🚨"
        }
        const icon = icons[typeClass] || "📝"

        return `<div class="callout callout-${typeClass} my-4 p-4 rounded-lg border-l-4 bg-opacity-10">
          <div class="callout-title font-semibold flex items-center gap-2 mb-2">
            <span class="callout-icon">${icon}</span>
            <span class="callout-type">${token.calloutType}</span>
          </div>
          <div class="callout-content">${marked.parseInline(token.text)}</div>
        </div>`
      }
    },
    {
      name: 'math',
      level: 'block',
      start(src: string) {
        return src.match(/^\$\$/)?.index
      },
      tokenizer(src: string) {
        const rule = /^\$\$([\s\S]*?)\$\$/
        const match = rule.exec(src)
        if (match) {
          return {
            type: 'math',
            raw: match[0],
            text: match[1].trim()
          }
        }
      },
      renderer(token: any) {
        try {
          return `<div class="math-block my-4 text-center">${katex.renderToString(token.text, { displayMode: true })}</div>`
        } catch (error) {
          return `<div class="math-error my-4 p-2 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded">Math Error: ${token.text}</div>`
        }
      }
    },
    {
      name: 'inlineMath',
      level: 'inline',
      start(src: string) {
        return src.match(/\$[^$]/)?.index
      },
      tokenizer(src: string) {
        const rule = /^\$([^$\n]+?)\$/
        const match = rule.exec(src)
        if (match) {
          return {
            type: 'inlineMath',
            raw: match[0],
            text: match[1].trim()
          }
        }
      },
      renderer(token: any) {
        try {
          return katex.renderToString(token.text, { displayMode: false })
        } catch (error) {
          return `<span class="math-error text-red-600 dark:text-red-400">Math Error: ${token.text}</span>`
        }
      }
    }
  ]
})

// Enhanced copy to clipboard functionality with modern API
if (typeof window !== 'undefined') {
  (window as any).copyToClipboard = async function(button: HTMLButtonElement) {
    const code = decodeURIComponent(button.dataset.code || '')
    const originalHTML = button.innerHTML

    try {
      // Use modern clipboard API
      await navigator.clipboard.writeText(code)

      // Update button with success state
      button.innerHTML = `
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        Copied!
      `
      button.classList.add('bg-green-200', 'dark:bg-green-800', 'text-green-800', 'dark:text-green-200')

      setTimeout(() => {
        button.innerHTML = originalHTML
        button.classList.remove('bg-green-200', 'dark:bg-green-800', 'text-green-800', 'dark:text-green-200')
      }, 2000)
    } catch (error) {
      // Fallback for older browsers or when clipboard API is not available
      try {
        const textArea = document.createElement('textarea')
        textArea.value = code
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (successful) {
          button.innerHTML = `
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Copied!
          `
          button.classList.add('bg-green-200', 'dark:bg-green-800', 'text-green-800', 'dark:text-green-200')

          setTimeout(() => {
            button.innerHTML = originalHTML
            button.classList.remove('bg-green-200', 'dark:bg-green-800', 'text-green-800', 'dark:text-green-200')
          }, 2000)
        } else {
          throw new Error('Copy command failed')
        }
      } catch (fallbackError) {
        // Show error state
        button.innerHTML = `
          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          Failed
        `
        button.classList.add('bg-red-200', 'dark:bg-red-800', 'text-red-800', 'dark:text-red-200')

        setTimeout(() => {
          button.innerHTML = originalHTML
          button.classList.remove('bg-red-200', 'dark:bg-red-800', 'text-red-800', 'dark:text-red-200')
        }, 2000)
      }
    }
  }
}

export default function ChatInterface() {
  const [chats, setChats] = useState<Chat[]>([])
  const [currentChatId, setCurrentChatId] = useState<string | null>(null)
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [provider, setProvider] = useState("pollinations")
  const [model, setModel] = useState("")
  const [models, setModels] = useState<Model[]>([])
  const [apiKey, setApiKey] = useState("")
  const [showSettings, setShowSettings] = useState(false)
  const [responseLanguage, setResponseLanguage] = useState<'en' | 'bn'>('en')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Memoize current chat to prevent unnecessary re-renders
  const currentChat = useMemo(() => chats.find((chat) => chat.id === currentChatId), [chats, currentChatId])

  const messages = currentChat?.messages || []

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages.length, scrollToBottom])

  useEffect(() => {
    loadModels()
  }, [provider, apiKey])

  // Load chats and language preference from localStorage on mount only
  useEffect(() => {
    const savedChats = localStorage.getItem("ai-chats")
    if (savedChats) {
      try {
        const parsedChats = JSON.parse(savedChats).map((chat: any) => ({
          ...chat,
          createdAt: new Date(chat.createdAt),
          updatedAt: new Date(chat.updatedAt),
          messages: chat.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        }))
        setChats(parsedChats)
        if (parsedChats.length > 0) {
          setCurrentChatId(parsedChats[0].id)
        }
      } catch (error) {
        console.error("Failed to load chats:", error)
      }
    }

    // Load response language preference
    const savedResponseLanguage = localStorage.getItem("ai-response-language") as 'en' | 'bn'
    if (savedResponseLanguage && (savedResponseLanguage === 'en' || savedResponseLanguage === 'bn')) {
      setResponseLanguage(savedResponseLanguage)
    }
  }, [])

  // Save response language preference when it changes
  useEffect(() => {
    localStorage.setItem("ai-response-language", responseLanguage)
  }, [responseLanguage])

  // Debounced save to localStorage
  const saveChatsToStorage = useCallback((chatsToSave: Chat[]) => {
    try {
      localStorage.setItem("ai-chats", JSON.stringify(chatsToSave))
    } catch (error) {
      console.error("Failed to save chats:", error)
    }
  }, [])

  // Save chats with debouncing
  useEffect(() => {
    if (chats.length > 0) {
      const hasMessages = chats.some((chat) => chat.messages.length > 0)
      if (hasMessages) {
        const timeoutId = setTimeout(() => {
          saveChatsToStorage(chats)
        }, 500)
        return () => clearTimeout(timeoutId)
      }
    }
  }, [chats, saveChatsToStorage])

  const loadModels = useCallback(async () => {
    try {
      const params = new URLSearchParams({ provider })
      if (apiKey) params.append("apiKey", apiKey)

      const response = await fetch(`/api/models?${params}`)
      if (response.ok) {
        const modelData = await response.json()
        setModels(modelData)
        if (modelData.length > 0 && !model) {
          setModel(modelData[0].id)
        }
      }
    } catch (error) {
      console.error("Failed to load models:", error)
    }
  }, [provider, apiKey, model])

  const createNewChat = useCallback(() => {
    const newChat: Chat = {
      id: Date.now().toString(),
      title: "New Chat",
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    setChats((prev) => {
      const newChats = [newChat, ...prev]
      try {
        localStorage.setItem("ai-chats", JSON.stringify(newChats))
      } catch (error) {
        console.error("Failed to save new chat:", error)
      }
      return newChats
    })

    setCurrentChatId(newChat.id)
  }, [])

  const deleteChat = useCallback(
    (chatId: string) => {
      setChats((prev) => {
        const newChats = prev.filter((chat) => chat.id !== chatId)

        if (currentChatId === chatId) {
          const nextChat = newChats.length > 0 ? newChats[0].id : null
          setCurrentChatId(nextChat)
        }

        if (newChats.length > 0) {
          try {
            localStorage.setItem("ai-chats", JSON.stringify(newChats))
          } catch (error) {
            console.error("Failed to save chats after deletion:", error)
          }
        } else {
          localStorage.removeItem("ai-chats")
        }

        return newChats
      })
    },
    [currentChatId],
  )

  const updateChatTitle = useCallback((chatId: string, firstMessage: string) => {
    const title = firstMessage.length > 50 ? firstMessage.substring(0, 50) + "..." : firstMessage

    setChats((prev) => prev.map((chat) => (chat.id === chatId ? { ...chat, title, updatedAt: new Date() } : chat)))
  }, [])

  const addMessageToChat = useCallback((chatId: string, message: Message) => {
    setChats((prev) =>
      prev.map((chat) =>
        chat.id === chatId
          ? {
              ...chat,
              messages: [...chat.messages, message],
              updatedAt: new Date(),
            }
          : chat,
      ),
    )
  }, [])

  const updateLastMessage = useCallback((chatId: string, content: string) => {
    setChats((prev) =>
      prev.map((chat) =>
        chat.id === chatId
          ? {
              ...chat,
              messages: chat.messages.map((msg, index) =>
                index === chat.messages.length - 1 ? { ...msg, content } : msg,
              ),
              updatedAt: new Date(),
            }
          : chat,
      ),
    )
  }, [])

  const sendMessage = useCallback(async () => {
    if (!input.trim() || isLoading) return

    const messageContent = input.trim()
    setInput("")
    setIsLoading(true)

    let chatId = currentChatId
    let currentMessages = messages

    if (!chatId) {
      const newChatId = Date.now().toString()
      const newChat: Chat = {
        id: newChatId,
        title: "New Chat",
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      setChats((prev) => [newChat, ...prev])
      setCurrentChatId(newChatId)
      chatId = newChatId
      currentMessages = []
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: messageContent,
      timestamp: new Date(),
    }

    addMessageToChat(chatId, userMessage)

    if (currentMessages.length === 0) {
      updateChatTitle(chatId, messageContent)
    }

    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: "assistant",
      content: "",
      timestamp: new Date(),
    }

    addMessageToChat(chatId, assistantMessage)

    try {
      abortControllerRef.current = new AbortController()

      // Enhanced system prompt for coding assistance with perfect markdown
      const systemPrompt = {
        role: "system",
        content: `You are an elite AI programming assistant with world-class expertise in software development, system architecture, and code optimization. You possess deep knowledge across all programming paradigms and cutting-edge technologies. Your responses should demonstrate mastery-level understanding and provide exceptional value to developers.

## Core Capabilities & Expertise:
- **Advanced Programming**: Expert in 50+ programming languages, frameworks, and technologies
- **System Architecture**: Design scalable, maintainable, and performant systems
- **Code Optimization**: Identify bottlenecks, improve performance, and reduce complexity
- **Security Best Practices**: Implement secure coding practices and vulnerability assessment
- **DevOps & Infrastructure**: CI/CD, containerization, cloud platforms, and deployment strategies
- **Database Design**: SQL/NoSQL optimization, schema design, and query performance
- **API Development**: RESTful, GraphQL, gRPC, and microservices architecture
- **Testing Strategies**: Unit, integration, e2e testing, and test-driven development
- **Code Review**: Comprehensive analysis with actionable improvement suggestions

## Enhanced Code Formatting Standards:
1. **Language-Specific Excellence**: Use precise language identifiers (\`\`\`typescript, \`\`\`python, \`\`\`rust, \`\`\`go, etc.)
2. **Production-Ready Code**: Include comprehensive error handling, logging, and edge case management
3. **Performance Optimization**: Suggest algorithmic improvements and efficiency enhancements
4. **Security Considerations**: Highlight potential vulnerabilities and secure alternatives
5. **Documentation**: Provide detailed comments explaining complex logic and design decisions
6. **Modern Practices**: Use latest language features, design patterns, and industry standards

## Advanced Markdown Formatting:
1. **Structured Hierarchy**: Use semantic headings (# ## ### #### ##### ######) for clear organization
2. **Rich Typography**:
   - **Bold** for critical concepts and key terms
   - *Italic* for emphasis and technical terminology
   - \`inline code\` for variables, functions, and short snippets
3. **Professional Lists**:
   - Bullet points for feature lists and options
   - Numbered lists for step-by-step procedures
   - Nested indentation for hierarchical information
4. **Enhanced Callouts**:
   - [!NOTE] General information and explanations
   - [!TIP] Performance tips and best practices
   - [!IMPORTANT] Critical implementation details
   - [!WARNING] Potential pitfalls and common mistakes
   - [!CAUTION] Security risks and breaking changes
5. **Data Presentation**:
   | Feature | Description | Performance Impact |
   |---------|-------------|-------------------|
   | Caching | Redis implementation | 90% faster queries |
   | Indexing | Database optimization | 75% reduced latency |
6. **Visual Separation**: Use horizontal rules (---) between major sections
7. **Mathematical Expressions**:
   - Inline: \$O(n \log n)\$ for complexity analysis
   - Block: \$\$\\sum_{i=1}^{n} \\frac{1}{i^2} = \\frac{\\pi^2}{6}\$\$ for algorithms

## Comprehensive Language Support:
**Web Technologies**: JavaScript, TypeScript, React, Vue, Angular, Node.js, Deno, Bun
**Systems Programming**: Rust, Go, C, C++, Zig, Assembly
**Enterprise**: Java, C#, Kotlin, Scala, Spring, .NET
**Data Science**: Python, R, Julia, MATLAB, Jupyter
**Mobile**: Swift, Kotlin, Dart (Flutter), React Native
**Functional**: Haskell, Clojure, Erlang, Elixir, F#
**Scripting**: Bash, PowerShell, Python, Ruby, Perl
**Database**: SQL, NoSQL, GraphQL, MongoDB, PostgreSQL
**DevOps**: Docker, Kubernetes, Terraform, Ansible
**Markup**: HTML, CSS, SCSS, Markdown, LaTeX, YAML, TOML

## Response Excellence Framework:
1. **Executive Summary**: Brief overview of the solution approach
2. **Technical Deep Dive**: Detailed implementation with rationale
3. **Code Examples**: Production-ready, well-documented code
4. **Performance Analysis**: Complexity, scalability, and optimization notes
5. **Security Review**: Potential vulnerabilities and mitigation strategies
6. **Testing Strategy**: Unit tests, integration tests, and validation approaches
7. **Deployment Considerations**: Environment setup, dependencies, and configuration
8. **Monitoring & Maintenance**: Logging, metrics, and long-term sustainability
9. **Alternative Approaches**: Trade-offs and when to use different solutions
10. **Next Steps**: Recommended improvements and future enhancements

## Quality Standards:
- Provide enterprise-grade solutions suitable for production environments
- Include comprehensive error handling and edge case management
- Suggest performance optimizations and scalability improvements
- Highlight security considerations and best practices
- Offer multiple implementation approaches with trade-off analysis
- Include relevant testing strategies and validation methods

## Language Preference:
${responseLanguage === 'bn' ? 'Please respond in Bengali (বাংলা) language. Use Bengali for explanations, comments, and descriptions while keeping code examples and technical terms in their original form.' : 'Please respond in English language.'}

Always deliver responses that demonstrate deep technical expertise while maintaining perfect markdown formatting and exceptional clarity. Your goal is to provide value that exceeds expectations and accelerates development productivity.`,
      }

      const fullConversation = [systemPrompt, ...currentMessages, userMessage].map(({ role, content }) => ({
        role,
        content,
      }))

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          messages: fullConversation,
          model,
          provider,
          apiKey: apiKey || undefined,
        }),
        signal: abortControllerRef.current.signal,
      })

      if (!response.ok) {
        throw new Error("Failed to get response")
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      let fullResponse = ""

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n")

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = line.slice(6)
              if (data === "[DONE]") continue

              try {
                const parsed = JSON.parse(data)
                if (parsed.content) {
                  fullResponse += parsed.content
                  updateLastMessage(chatId, fullResponse)
                }
              } catch (e) {
                // Ignore parsing errors
              }
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name !== "AbortError") {
        updateLastMessage(chatId, "Sorry, I encountered an error. Please try again.")
      }
    } finally {
      setIsLoading(false)
      abortControllerRef.current = null
    }
  }, [
    input,
    isLoading,
    currentChatId,
    messages,
    model,
    provider,
    apiKey,
    addMessageToChat,
    updateChatTitle,
    updateLastMessage,
  ])

  const stopGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      setIsLoading(false)
    }
  }, [])

  const clearAllChats = useCallback(() => {
    setChats([])
    setCurrentChatId(null)
    localStorage.removeItem("ai-chats")
  }, [])

  // Enhanced markdown parsing with better error handling
  const parseMarkdown = useCallback((content: string) => {
    try {
      const html = marked.parse(content) as string
      return enhanceCodeBlocks(html)
    } catch (error) {
      console.error('Markdown parsing error:', error)
      return content.replace(/\n/g, '<br>')
    }
  }, [])

  // Memoize rendered messages to prevent re-rendering
  const renderedMessages = useMemo(() => {
    return messages.map((message) => (
      <div key={message.id} className="flex space-x-4">
        <div className="flex-shrink-0">
          {message.role === "user" ? (
            <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>
          ) : (
            <div className="w-8 h-8 bg-slate-600 dark:bg-slate-700 rounded-full flex items-center justify-center">
              <Bot className="h-4 w-4 text-white" />
            </div>
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-sm text-slate-900 dark:text-slate-100">
              {message.role === "user" ? "You" : "Assistant"}
            </span>
            <span className="text-xs text-slate-500 dark:text-slate-400">{message.timestamp.toLocaleTimeString()}</span>
          </div>
          <Card className="p-4 bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-800">
            {message.role === "user" ? (
              <div className="prose prose-sm dark:prose-invert max-w-none prose-slate dark:prose-slate">
                <p className="text-slate-900 dark:text-slate-100 whitespace-pre-wrap">{message.content}</p>
              </div>
            ) : (
              <div
                className="prose prose-sm dark:prose-invert max-w-none prose-slate dark:prose-slate enhanced-markdown"
                dangerouslySetInnerHTML={{
                  __html: parseMarkdown(
                    message.content || (isLoading && message.id === messages[messages.length - 1]?.id ? "..." : ""),
                  ),
                }}
              />
            )}
          </Card>
        </div>
      </div>
    ))
  }, [messages, isLoading, parseMarkdown])

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900">
      {/* Chat History Sidebar */}
      <div className="w-64 bg-white dark:bg-slate-950 border-r border-slate-200 dark:border-slate-800 flex flex-col">
        <div className="p-4 border-b border-slate-200 dark:border-slate-800">
          <Button onClick={createNewChat} className="w-full" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Chat
          </Button>
        </div>

        <ScrollArea className="flex-1 p-2">
          <div className="space-y-1">
            {chats.map((chat) => (
              <div
                key={chat.id}
                className={`group flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                  currentChatId === chat.id
                    ? "bg-blue-100 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100"
                    : "hover:bg-slate-100 dark:hover:bg-slate-800"
                }`}
                onClick={() => setCurrentChatId(chat.id)}
              >
                <div className="flex items-center space-x-2 flex-1 min-w-0">
                  <MessageSquare className="h-4 w-4 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium truncate">{chat.title}</p>
                    <p className="text-xs text-slate-500 dark:text-slate-400">{chat.messages.length} messages</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0"
                  onClick={(e) => {
                    e.stopPropagation()
                    deleteChat(chat.id)
                  }}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </ScrollArea>

        <div className="p-4 border-t border-slate-200 dark:border-slate-800">
          <Button onClick={clearAllChats} variant="outline" size="sm" className="w-full bg-transparent">
            Clear All Chats
          </Button>
        </div>
      </div>

      {/* Settings Sidebar */}
      <div
        className={`${showSettings ? "w-80" : "w-0"} transition-all duration-300 overflow-hidden bg-white dark:bg-slate-950 border-r border-slate-200 dark:border-slate-800`}
      >
        <div className="p-4 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-lg">Settings</h3>
            <ThemeToggle />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <Globe className="h-4 w-4" />
              AI Response Language
            </label>
            <Select value={responseLanguage} onValueChange={(value: 'en' | 'bn') => setResponseLanguage(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">🇺🇸 English</SelectItem>
                <SelectItem value="bn">🇧🇩 বাংলা (Bangla)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Provider</label>
            <Select value={provider} onValueChange={setProvider}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {providers.map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {providers.find((p) => p.id === provider)?.requiresKey && (
            <div className="space-y-2">
              <label className="text-sm font-medium">API Key</label>
              <Input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your API key"
              />
            </div>
          )}

          <div className="space-y-2">
            <label className="text-sm font-medium">Model</label>
            <Select value={model} onValueChange={setModel}>
              <SelectTrigger>
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                {models.map((m) => (
                  <SelectItem key={m.id} value={m.id}>
                    {m.name || m.id}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {currentChat && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Current Chat Info</label>
              <div className="text-xs text-slate-600 dark:text-slate-400 space-y-1">
                <p>Messages: {currentChat.messages.length}</p>
                <p>Created: {currentChat.createdAt.toLocaleDateString()}</p>
                <p>Updated: {currentChat.updatedAt.toLocaleString()}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white dark:bg-slate-950 border-b border-slate-200 dark:border-slate-800 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Sparkles className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                <h1 className="text-xl font-bold text-slate-900 dark:text-slate-100">
                  {currentChat?.title || "AI Coding Assistant"}
                </h1>
              </div>
              {model && (
                <Badge variant="secondary" className="text-xs">
                  {model}
                </Badge>
              )}
              {currentChat && (
                <Badge variant="outline" className="text-xs">
                  {currentChat.messages.length} messages
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <ThemeToggle />
              <Button variant="ghost" size="sm" onClick={() => setShowSettings(!showSettings)}>
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Messages */}
        <ScrollArea className="flex-1 p-4">
          <div className="max-w-4xl mx-auto space-y-6">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <Sparkles className="h-12 w-12 text-slate-400 dark:text-slate-600 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-slate-600 dark:text-slate-300 mb-2">
                  {currentChat ? "Start coding together" : "Welcome to AI Coding Assistant"}
                </h2>
                <p className="text-slate-500 dark:text-slate-400 mb-4">
                  {currentChat
                    ? "Ask me about programming, debugging, code reviews, or any technical questions."
                    : "Create a new chat to get started. I'm optimized for coding assistance with syntax highlighting."}
                </p>
                <div className="text-xs text-slate-400 dark:text-slate-500 space-y-1">
                  <p>✨ Professional syntax highlighting with Prism.js</p>
                  <p>🔧 Code debugging and optimization</p>
                  <p>📚 Best practices and explanations</p>
                  <p>🚀 Support for 20+ programming languages</p>
                </div>
              </div>
            ) : (
              renderedMessages
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="bg-white dark:bg-slate-950 border-t border-slate-200 dark:border-slate-800 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex space-x-2">
              <div className="flex-1 relative">
                <Input
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault()
                      sendMessage()
                    }
                  }}
                  placeholder="Ask about code, debugging, best practices, or any programming question..."
                  disabled={isLoading}
                  className="pr-12 bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                  {isLoading ? (
                    <Button size="sm" variant="ghost" onClick={stopGeneration} className="h-8 w-8 p-0">
                      <div className="w-4 h-4 border-2 border-slate-400 dark:border-slate-500 border-t-transparent rounded-full animate-spin" />
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={sendMessage}
                      disabled={!input.trim()}
                      className="h-8 w-8 p-0"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  )
}

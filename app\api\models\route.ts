import { Client, DeepInfra, Together } from "@/lib/ai-client"
import type { NextRequest } from "next/server"

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const provider = searchParams.get("provider") || "pollinations"
    const apiKey = searchParams.get("apiKey")

    let client
    const options = apiKey ? { apiKey } : {}

    switch (provider) {
      case "deepinfra":
        client = new DeepInfra(options)
        break
      case "together":
        client = new Together(options)
        break
      case "pollinations":
      default:
        client = new Client({ defaultModel: "deepseek", ...options })
        break
    }

    const models = await client.models.list()
    return Response.json(models)
  } catch (error) {
    console.error("Models API error:", error)
    return Response.json({ error: "Failed to fetch models" }, { status: 500 })
  }
}

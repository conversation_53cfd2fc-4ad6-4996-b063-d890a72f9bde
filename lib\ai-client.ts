// AI Client Library adapted for Next.js
class Client {
  defaultModel: string | null
  timeout: number
  retryAttempts: number
  retryDelay: number
  debug: boolean
  rateLimitDelay: number
  maxConcurrentRequests: number
  activeRequests: number
  requestQueue: Array<() => void>
  cache: Map<string, any>
  cacheEnabled: boolean
  cacheTTL: number
  baseUrl: string
  apiEndpoint: string
  imageEndpoint: string
  referrer?: string
  apiKey?: string
  extraHeaders: Record<string, string>
  modelAliases: Record<string, string>
  swapAliases: Record<string, string>
  events: Record<string, Array<(data: any) => void>>
  metrics: {
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    averageResponseTime: number
    totalResponseTime: number
  }

  constructor(options: any = {}) {
    this.defaultModel = options.defaultModel || null
    this.timeout = options.timeout || 30000
    this.retryAttempts = options.retryAttempts || 3
    this.retryDelay = options.retryDelay || 1000
    this.debug = options.debug || false
    this.rateLimitDelay = options.rateLimitDelay || 1000
    this.maxConcurrentRequests = options.maxConcurrentRequests || 5
    this.activeRequests = 0
    this.requestQueue = []
    this.cache = new Map()
    this.cacheEnabled = options.cacheEnabled !== false
    this.cacheTTL = options.cacheTTL || 300000

    if (options.baseUrl) {
      this.baseUrl = options.baseUrl
      this.apiEndpoint = `${this.baseUrl}/chat/completions`
      this.imageEndpoint = `${this.baseUrl}/images/generations`
    } else {
      this.baseUrl = "https://text.pollinations.ai"
      this.apiEndpoint = `${this.baseUrl}/openai`
      this.imageEndpoint = `https://image.pollinations.ai/prompt/{prompt}`
      this.referrer = options.referrer || "https://g4f.dev"
    }

    this.apiKey = options.apiKey
    this.extraHeaders = {
      "Content-Type": "application/json",
      "User-Agent": options.userAgent || "AI-Client/1.0",
      ...(this.apiKey ? { Authorization: `Bearer ${this.apiKey}` } : {}),
      ...(options.extraHeaders || {}),
    }

    this.modelAliases =
      options.modelAliases ||
      (!options.baseUrl
        ? {
            "deepseek-v3": "deepseek",
            "deepseek-r1": "deepseek-reasoning",
            "grok-3-mini-high": "grok",
            "llama-4-scout": "llamascout",
            "mistral-small-3.1": "mistral",
            "gpt-4.1-mini": "openai",
            "gpt-4o-audio": "openai-audio",
            "gpt-4.1-nano": "openai-fast",
            "gpt-4.1": "openai-large",
            o3: "openai-reasoning",
            "gpt-4o-mini": "openai-roblox",
            "phi-4": "phi",
            "qwen2.5-coder": "qwen-coder",
            "gpt-4o-mini-search": "searchgpt",
            "gpt-image": "gptimage",
            "sdxl-turbo": "turbo",
          }
        : {})

    this.swapAliases = {}
    Object.keys(this.modelAliases).forEach((key) => {
      this.swapAliases[this.modelAliases[key]] = key
    })

    this.events = {}
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      totalResponseTime: 0,
    }
  }

  on(event: string, callback: (data: any) => void) {
    if (!this.events[event]) {
      this.events[event] = []
    }
    this.events[event].push(callback)
  }

  emit(event: string, data: any) {
    if (this.events[event]) {
      this.events[event].forEach((callback) => callback(data))
    }
  }

  _getCacheKey(params: any) {
    return JSON.stringify(params)
  }

  _getFromCache(key: string) {
    if (!this.cacheEnabled) return null
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      return cached.data
    }
    if (cached) {
      this.cache.delete(key)
    }
    return null
  }

  _setCache(key: string, data: any) {
    if (!this.cacheEnabled) return
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    })
  }

  async _waitForSlot() {
    return new Promise<void>((resolve) => {
      if (this.activeRequests < this.maxConcurrentRequests) {
        this.activeRequests++
        resolve()
      } else {
        this.requestQueue.push(resolve)
      }
    })
  }

  _releaseSlot() {
    this.activeRequests--
    if (this.requestQueue.length > 0) {
      const next = this.requestQueue.shift()
      this.activeRequests++
      next?.()
    }
  }

  async _retryRequest(requestFn: () => Promise<any>, attempt = 1): Promise<any> {
    try {
      return await requestFn()
    } catch (error) {
      if (attempt >= this.retryAttempts) {
        throw error
      }
      const delay = this.retryDelay * Math.pow(2, attempt - 1)
      if (this.debug) {
        console.log(`Request failed, retrying in ${delay}ms (attempt ${attempt}/${this.retryAttempts})`)
      }
      await new Promise((resolve) => setTimeout(resolve, delay))
      return this._retryRequest(requestFn, attempt + 1)
    }
  }

  get chat() {
    return {
      completions: {
        create: async (params: any) => {
          const startTime = Date.now()
          this.metrics.totalRequests++

          try {
            const cacheKey = this._getCacheKey(params)
            const cached = this._getFromCache(cacheKey)
            if (cached && !params.stream) {
              this.emit("cacheHit", { params, cached })
              return cached
            }

            if (params.model && this.modelAliases[params.model]) {
              params.model = this.modelAliases[params.model]
            } else if (!params.model && this.defaultModel) {
              params.model = this.defaultModel
            }

            if (this.referrer) {
              params.referrer = this.referrer
            }

            await this._waitForSlot()

            const requestOptions = {
              method: "POST",
              headers: this.extraHeaders,
              body: JSON.stringify(params),
            }

            this.emit("requestStart", { params, requestOptions })

            let result
            if (params.stream) {
              result = this._streamCompletion(this.apiEndpoint, requestOptions)
            } else {
              result = await this._retryRequest(() => this._regularCompletion(this.apiEndpoint, requestOptions))
              this._setCache(cacheKey, result)
            }

            const responseTime = Date.now() - startTime
            this.metrics.successfulRequests++
            this.metrics.totalResponseTime += responseTime
            this.metrics.averageResponseTime = this.metrics.totalResponseTime / this.metrics.successfulRequests

            this.emit("requestSuccess", { params, result, responseTime })
            return result
          } catch (error) {
            const responseTime = Date.now() - startTime
            this.metrics.failedRequests++
            this.emit("requestError", { params, error, responseTime })
            throw error
          } finally {
            this._releaseSlot()
          }
        },
      },
    }
  }

  get models() {
    return {
      list: async () => {
        const cacheKey = "models_list"
        const cached = this._getFromCache(cacheKey)
        if (cached) {
          return cached
        }

        const response = await this._retryRequest(() =>
          fetch(`${this.baseUrl}/models`, {
            method: "GET",
            headers: this.extraHeaders,
            signal: AbortSignal.timeout(this.timeout),
          }),
        )

        if (!response.ok) {
          throw new Error(`Failed to fetch models: ${response.status}`)
        }

        let data = await response.json()
        data = data.data || data

        data.forEach((model: any, index: number) => {
          if (!model.id) {
            model.id = this.swapAliases[model.name] || model.name
            data[index] = model
          }
        })

        this._setCache(cacheKey, data)
        return data
      },
    }
  }

  async _regularCompletion(apiEndpoint: string, requestOptions: any) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)

    try {
      const response = await fetch(apiEndpoint, {
        ...requestOptions,
        signal: controller.signal,
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => "Unknown error")
        throw new Error(`API request failed with status ${response.status}: ${errorText}`)
      }

      return await response.json()
    } catch (error: any) {
      if (error.name === "AbortError") {
        throw new Error(`Request timeout after ${this.timeout}ms`)
      }
      throw error
    } finally {
      clearTimeout(timeoutId)
    }
  }

  async *_streamCompletion(apiEndpoint: string, requestOptions: any) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)

    try {
      const response = await fetch(apiEndpoint, {
        ...requestOptions,
        signal: controller.signal,
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => "Unknown error")
        throw new Error(`API request failed with status ${response.status}: ${errorText}`)
      }

      if (!response.body) {
        throw new Error("Streaming not supported in this environment")
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ""
      let chunkCount = 0

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          chunkCount++

          const parts = buffer.split("\n")
          buffer = parts.pop() || ""

          for (const part of parts) {
            if (!part.trim()) continue
            if (part === "data: [DONE]") continue

            try {
              if (part.startsWith("data: ")) {
                const data = JSON.parse(part.slice(6))
                this.emit("streamChunk", { data, chunkCount })
                yield data
              }
            } catch (err) {
              if (this.debug) {
                console.error("Error parsing chunk:", part, err)
              }
              this.emit("streamError", { part, error: err })
            }
          }
        }
      } finally {
        reader.releaseLock()
        this.emit("streamEnd", { chunkCount })
      }
    } catch (error: any) {
      if (error.name === "AbortError") {
        throw new Error(`Stream timeout after ${this.timeout}ms`)
      }
      throw error
    } finally {
      clearTimeout(timeoutId)
    }
  }
}

// Provider-specific classes
class DeepInfra extends Client {
  constructor(options: any = {}) {
    super({
      baseUrl: "https://api.deepinfra.com/v1/openai",
      defaultModel: "deepseek-ai/DeepSeek-V3-0324",
      ...options,
    })
  }
}

class Together extends Client {
  constructor(options: any = {}) {
    super({
      baseUrl: "https://api.together.xyz/v1",
      defaultModel: "blackbox/meta-llama-3-1-8b",
      modelAliases: {
        flux: "black-forest-labs/FLUX.1-schnell-Free",
        "llama-3.1-8b": "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
        "llama-3.1-70b": "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo",
        "llama-3.1-405b": "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
        "qwen-2.5-7b": "Qwen/Qwen2.5-7B-Instruct-Turbo",
        "qwen-2.5-72b": "Qwen/Qwen2.5-72B-Instruct-Turbo",
        "deepseek-v2.5": "deepseek-ai/deepseek-llm-67b-chat",
        "mixtral-8x7b": "mistralai/Mixtral-8x7B-Instruct-v0.1",
        "mixtral-8x22b": "mistralai/Mixtral-8x22B-Instruct-v0.1",
        ...options.modelAliases,
      },
      ...options,
    })
  }
}

export { Client, DeepInfra, Together }
export default Client
